import asyncio
import aiohttp
import time
import json
from typing import Dict, List, Any, Optional
from urllib.parse import urlencode


class BlastValidator:
    """
    Async validator for primer pairs using NCBI BLAST API.
    """
    
    def __init__(self):
        self.blast_url = "https://blast.ncbi.nlm.nih.gov/Blast.cgi"
        self.max_wait_time = 300  # Maximum wait time in seconds (5 minutes)
        self.poll_interval = 10   # Poll every 10 seconds
    
    async def validate_primer_pair(self, fwd_primer: str, rev_primer: str, species_db: str = "nr") -> Dict[str, Any]:
        """
        Validate primer pair specificity using NCBI BLAST.
        
        Args:
            fwd_primer: Forward primer sequence
            rev_primer: Reverse primer sequence
            species_db: Database to search against (default: "nr")
            
        Returns:
            Dictionary containing validation results
        """
        try:
            # Validate both primers
            fwd_results = await self._blast_sequence(fwd_primer, species_db, "forward")
            rev_results = await self._blast_sequence(rev_primer, species_db, "reverse")
            
            # Combine and analyze results
            validation_summary = self._analyze_blast_results(fwd_results, rev_results)
            
            return validation_summary
            
        except Exception as e:
            return {
                'error': f'BLAST validation failed: {str(e)}',
                'target_match': None,
                'off_target_hits': [],
                'specificity_score': 0.0
            }
    
    async def _blast_sequence(self, sequence: str, database: str, primer_type: str) -> Dict[str, Any]:
        """
        Submit a single sequence to BLAST and retrieve results.
        
        Args:
            sequence: DNA sequence to BLAST
            database: BLAST database to search
            primer_type: "forward" or "reverse" for identification
            
        Returns:
            BLAST results dictionary
        """
        async with aiohttp.ClientSession() as session:
            # Step 1: Submit BLAST job
            rid = await self._submit_blast_job(session, sequence, database)
            
            if not rid:
                return {'error': 'Failed to submit BLAST job', 'primer_type': primer_type}
            
            # Step 2: Poll for completion
            results = await self._poll_blast_results(session, rid)
            
            if results:
                results['primer_type'] = primer_type
                results['sequence'] = sequence
            
            return results or {'error': 'Failed to retrieve BLAST results', 'primer_type': primer_type}
    
    async def _submit_blast_job(self, session: aiohttp.ClientSession, sequence: str, database: str) -> Optional[str]:
        """
        Submit BLAST job and return Request ID (RID).
        """
        # Create FASTA format sequence
        fasta_sequence = f">query\n{sequence}"
        
        # BLAST parameters
        params = {
            'CMD': 'Put',
            'PROGRAM': 'blastn',
            'DATABASE': database,
            'QUERY': fasta_sequence,
            'FORMAT_TYPE': 'JSON2',
            'EXPECT': '10',
            'WORD_SIZE': '7',  # Shorter word size for primer sequences
            'MATCH_REWARD': '2',
            'MISMATCH_PENALTY': '-3',
            'GAPCOSTS': '5 2'
        }
        
        try:
            async with session.post(self.blast_url, data=params) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # Extract RID from response
                    for line in content.split('\n'):
                        if line.startswith('RID = '):
                            return line.split('=')[1].strip()
                
                return None
                
        except Exception as e:
            print(f"Error submitting BLAST job: {str(e)}")
            return None
    
    async def _poll_blast_results(self, session: aiohttp.ClientSession, rid: str) -> Optional[Dict[str, Any]]:
        """
        Poll BLAST results until completion.
        """
        start_time = time.time()
        
        while time.time() - start_time < self.max_wait_time:
            try:
                # Check job status
                status_params = {
                    'CMD': 'Get',
                    'FORMAT_OBJECT': 'SearchInfo',
                    'RID': rid
                }
                
                async with session.get(self.blast_url, params=status_params) as response:
                    if response.status == 200:
                        status_content = await response.text()
                        
                        if 'Status=WAITING' in status_content:
                            await asyncio.sleep(self.poll_interval)
                            continue
                        elif 'Status=FAILED' in status_content:
                            return {'error': 'BLAST job failed'}
                        elif 'Status=UNKNOWN' in status_content:
                            return {'error': 'BLAST job status unknown'}
                        elif 'Status=READY' in status_content:
                            # Job is complete, retrieve results
                            return await self._retrieve_blast_results(session, rid)
                
            except Exception as e:
                print(f"Error polling BLAST results: {str(e)}")
                await asyncio.sleep(self.poll_interval)
        
        return {'error': 'BLAST job timed out'}
    
    async def _retrieve_blast_results(self, session: aiohttp.ClientSession, rid: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve final BLAST results in JSON format.
        """
        try:
            result_params = {
                'CMD': 'Get',
                'FORMAT_TYPE': 'JSON2',
                'RID': rid
            }
            
            async with session.get(self.blast_url, params=result_params) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # Parse JSON results
                    try:
                        results = json.loads(content)
                        return self._parse_blast_json(results)
                    except json.JSONDecodeError:
                        return {'error': 'Failed to parse BLAST JSON results'}
                
                return {'error': f'Failed to retrieve results: HTTP {response.status}'}
                
        except Exception as e:
            return {'error': f'Error retrieving BLAST results: {str(e)}'}
    
    def _parse_blast_json(self, blast_json: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse BLAST JSON results to extract relevant information.
        """
        try:
            # Navigate through BLAST JSON structure
            blast_output = blast_json.get('BlastOutput2', [])
            if not blast_output:
                return {'hits': [], 'num_hits': 0}
            
            report = blast_output[0].get('report', {})
            results = report.get('results', {})
            search = results.get('search', {})
            hits = search.get('hits', [])
            
            parsed_hits = []
            
            for hit in hits[:10]:  # Limit to top 10 hits
                hit_data = {
                    'accession': hit.get('description', [{}])[0].get('accession', 'Unknown'),
                    'title': hit.get('description', [{}])[0].get('title', 'Unknown'),
                    'length': hit.get('len', 0),
                    'hsps': []
                }
                
                # Parse HSPs (High-scoring Segment Pairs)
                for hsp in hit.get('hsps', []):
                    hsp_data = {
                        'score': hsp.get('score', 0),
                        'evalue': hsp.get('evalue', 1.0),
                        'identity': hsp.get('identity', 0),
                        'align_len': hsp.get('align_len', 0),
                        'query_from': hsp.get('query_from', 0),
                        'query_to': hsp.get('query_to', 0),
                        'hit_from': hsp.get('hit_from', 0),
                        'hit_to': hsp.get('hit_to', 0)
                    }
                    hit_data['hsps'].append(hsp_data)
                
                parsed_hits.append(hit_data)
            
            return {
                'hits': parsed_hits,
                'num_hits': len(hits)
            }
            
        except Exception as e:
            return {'error': f'Error parsing BLAST results: {str(e)}', 'hits': [], 'num_hits': 0}
    
    def _analyze_blast_results(self, fwd_results: Dict[str, Any], rev_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze BLAST results for both primers and generate specificity summary.
        """
        # Check for errors
        if 'error' in fwd_results or 'error' in rev_results:
            return {
                'error': 'BLAST analysis incomplete',
                'forward_error': fwd_results.get('error'),
                'reverse_error': rev_results.get('error'),
                'target_match': None,
                'off_target_hits': [],
                'specificity_score': 0.0
            }
        
        # Analyze hits
        fwd_hits = fwd_results.get('hits', [])
        rev_hits = rev_results.get('hits', [])
        
        # Find best target match (highest scoring hit for both primers)
        target_match = self._find_target_match(fwd_hits, rev_hits)
        
        # Identify off-target hits
        off_target_hits = self._identify_off_targets(fwd_hits, rev_hits, target_match)
        
        # Calculate specificity score
        specificity_score = self._calculate_specificity_score(target_match, off_target_hits)
        
        return {
            'target_match': target_match,
            'off_target_hits': off_target_hits,
            'specificity_score': specificity_score,
            'forward_hits': len(fwd_hits),
            'reverse_hits': len(rev_hits)
        }
    
    def _find_target_match(self, fwd_hits: List[Dict], rev_hits: List[Dict]) -> Optional[Dict[str, Any]]:
        """Find the best target match for both primers."""
        if not fwd_hits or not rev_hits:
            return None
        
        # Use the highest scoring hit from forward primer as target
        best_fwd = fwd_hits[0] if fwd_hits else None
        
        if best_fwd and best_fwd.get('hsps'):
            return {
                'accession': best_fwd['accession'],
                'title': best_fwd['title'],
                'forward_score': best_fwd['hsps'][0].get('score', 0),
                'forward_evalue': best_fwd['hsps'][0].get('evalue', 1.0),
                'forward_identity': best_fwd['hsps'][0].get('identity', 0)
            }
        
        return None
    
    def _identify_off_targets(self, fwd_hits: List[Dict], rev_hits: List[Dict], target_match: Optional[Dict]) -> List[Dict[str, Any]]:
        """Identify potential off-target hits."""
        off_targets = []
        
        # Consider hits with significant scores as potential off-targets
        for hit in fwd_hits[1:5]:  # Skip first hit (target), check next 4
            if hit.get('hsps'):
                hsp = hit['hsps'][0]
                if hsp.get('evalue', 1.0) < 0.01:  # Significant e-value threshold
                    off_targets.append({
                        'accession': hit['accession'],
                        'title': hit['title'],
                        'score': hsp.get('score', 0),
                        'evalue': hsp.get('evalue', 1.0),
                        'primer': 'forward'
                    })
        
        return off_targets
    
    def _calculate_specificity_score(self, target_match: Optional[Dict], off_target_hits: List[Dict]) -> float:
        """Calculate a specificity score (0-1, higher is better)."""
        if not target_match:
            return 0.0
        
        # Base score from target match quality
        target_score = min(target_match.get('forward_score', 0) / 100.0, 1.0)
        
        # Penalty for off-target hits
        off_target_penalty = len(off_target_hits) * 0.1
        
        # Final score
        specificity_score = max(0.0, target_score - off_target_penalty)
        
        return round(specificity_score, 3)


# Example usage and testing
async def test_blast_validator():
    """Test function for the BlastValidator class."""
    validator = BlastValidator()
    
    # Test with short primer sequences
    fwd_primer = "ATGGAGGAGCCGCAGTCAGAT"
    rev_primer = "TCAGTCTGAGTCAGGCCCTTC"
    
    print("Testing BLAST validation...")
    results = await validator.validate_primer_pair(fwd_primer, rev_primer)
    
    print(f"Validation Results:")
    print(f"  Specificity Score: {results.get('specificity_score', 'N/A')}")
    print(f"  Target Match: {results.get('target_match', 'None')}")
    print(f"  Off-target Hits: {len(results.get('off_target_hits', []))}")


if __name__ == "__main__":
    asyncio.run(test_blast_validator())
