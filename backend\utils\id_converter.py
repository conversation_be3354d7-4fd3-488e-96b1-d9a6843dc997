import asyncio
import aiohttp
import mygene
from typing import Dict, Any, Optional
import xml.etree.ElementTree as ET
import time


class GeneIDConverter:
    """
    Utility class for converting and normalizing gene identifiers.
    """
    
    def __init__(self):
        self.mg = mygene.MyGeneInfo()
        self.ncbi_base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils"
        self.request_delay = 0.5  # Delay between requests to be respectful to APIs
    
    async def get_canonical_gene_info(self, gene_identifier: str, species: str) -> Dict[str, Any]:
        """
        Get canonical gene information for a given gene identifier and species.
        
        Args:
            gene_identifier: Gene symbol, alias, or ID to normalize
            species: Species name (e.g., "Homo sapiens", "Mus musculus")
            
        Returns:
            Dictionary containing standardized gene identifiers
        """
        try:
            # First try MyGene.info for comprehensive gene information
            mygene_result = await self._query_mygene(gene_identifier, species)
            
            if mygene_result:
                return mygene_result
            
            # Fallback to NCBI E-utilities if MyGene.info fails
            ncbi_result = await self._query_ncbi_gene(gene_identifier, species)
            
            if ncbi_result:
                return ncbi_result
            
            # If all methods fail, return basic information
            return {
                'hugo_symbol': gene_identifier.upper(),
                'entrez_id': None,
                'ensembl_id': None,
                'species': species,
                'aliases': [],
                'description': 'Gene information not found',
                'source': 'input_only'
            }
            
        except Exception as e:
            print(f"Error in gene ID conversion: {str(e)}")
            return {
                'hugo_symbol': gene_identifier.upper(),
                'entrez_id': None,
                'ensembl_id': None,
                'species': species,
                'aliases': [],
                'description': f'Error: {str(e)}',
                'source': 'error'
            }
    
    async def _query_mygene(self, gene_identifier: str, species: str) -> Optional[Dict[str, Any]]:
        """
        Query MyGene.info for gene information.
        """
        try:
            # Map species names to taxonomy IDs
            species_mapping = {
                'homo sapiens': 9606,
                'human': 9606,
                'mus musculus': 10090,
                'mouse': 10090,
                'rattus norvegicus': 10116,
                'rat': 10116,
                'danio rerio': 7955,
                'zebrafish': 7955,
                'drosophila melanogaster': 7227,
                'fruit fly': 7227,
                'caenorhabditis elegans': 6239,
                'c. elegans': 6239,
                'saccharomyces cerevisiae': 4932,
                'yeast': 4932
            }
            
            species_lower = species.lower().strip()
            taxid = species_mapping.get(species_lower, 9606)  # Default to human
            
            # Run the synchronous MyGene query in a thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, 
                self._mygene_query_sync, 
                gene_identifier, 
                taxid
            )
            
            return result
            
        except Exception as e:
            print(f"Error querying MyGene.info: {str(e)}")
            return None
    
    def _mygene_query_sync(self, gene_identifier: str, taxid: int) -> Optional[Dict[str, Any]]:
        """
        Synchronous MyGene.info query (to be run in thread pool).
        """
        try:
            # Query MyGene.info
            results = self.mg.query(
                gene_identifier, 
                species=taxid,
                fields='symbol,name,alias,entrezgene,ensembl.gene,summary',
                size=1
            )
            
            if results and 'hits' in results and results['hits']:
                hit = results['hits'][0]
                
                # Extract gene information
                gene_info = {
                    'hugo_symbol': hit.get('symbol', gene_identifier.upper()),
                    'entrez_id': str(hit.get('entrezgene', '')),
                    'ensembl_id': '',
                    'species': self._taxid_to_species(taxid),
                    'aliases': [],
                    'description': hit.get('name', ''),
                    'summary': hit.get('summary', ''),
                    'source': 'mygene'
                }
                
                # Extract Ensembl ID
                if 'ensembl' in hit and isinstance(hit['ensembl'], dict):
                    gene_info['ensembl_id'] = hit['ensembl'].get('gene', '')
                elif 'ensembl' in hit and isinstance(hit['ensembl'], list):
                    if hit['ensembl']:
                        gene_info['ensembl_id'] = hit['ensembl'][0].get('gene', '')
                
                # Extract aliases
                if 'alias' in hit:
                    if isinstance(hit['alias'], list):
                        gene_info['aliases'] = hit['alias']
                    elif isinstance(hit['alias'], str):
                        gene_info['aliases'] = [hit['alias']]
                
                return gene_info
            
            return None
            
        except Exception as e:
            print(f"Error in synchronous MyGene query: {str(e)}")
            return None
    
    def _taxid_to_species(self, taxid: int) -> str:
        """Convert taxonomy ID back to species name."""
        taxid_mapping = {
            9606: 'Homo sapiens',
            10090: 'Mus musculus',
            10116: 'Rattus norvegicus',
            7955: 'Danio rerio',
            7227: 'Drosophila melanogaster',
            6239: 'Caenorhabditis elegans',
            4932: 'Saccharomyces cerevisiae'
        }
        return taxid_mapping.get(taxid, 'Unknown species')
    
    async def _query_ncbi_gene(self, gene_identifier: str, species: str) -> Optional[Dict[str, Any]]:
        """
        Fallback query using NCBI E-utilities.
        """
        try:
            async with aiohttp.ClientSession() as session:
                # Search for gene in NCBI Gene database
                search_url = f"{self.ncbi_base_url}/esearch.fcgi"
                search_params = {
                    'db': 'gene',
                    'term': f'{gene_identifier}[Gene Name] AND {species}[Organism]',
                    'retmax': '1',
                    'retmode': 'xml'
                }
                
                await asyncio.sleep(self.request_delay)
                
                async with session.get(search_url, params=search_params) as response:
                    if response.status == 200:
                        content = await response.text()
                        root = ET.fromstring(content)
                        
                        # Extract gene ID
                        id_list = root.find('.//IdList')
                        if id_list is not None and len(id_list) > 0:
                            gene_id = id_list[0].text
                            
                            # Get detailed gene information
                            gene_details = await self._get_ncbi_gene_details(session, gene_id)
                            if gene_details:
                                return gene_details
                
                return None
                
        except Exception as e:
            print(f"Error querying NCBI Gene: {str(e)}")
            return None
    
    async def _get_ncbi_gene_details(self, session: aiohttp.ClientSession, gene_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed gene information from NCBI using gene ID.
        """
        try:
            summary_url = f"{self.ncbi_base_url}/esummary.fcgi"
            summary_params = {
                'db': 'gene',
                'id': gene_id,
                'retmode': 'xml'
            }
            
            await asyncio.sleep(self.request_delay)
            
            async with session.get(summary_url, params=summary_params) as response:
                if response.status == 200:
                    content = await response.text()
                    root = ET.fromstring(content)
                    
                    # Parse gene information
                    gene_info = {
                        'hugo_symbol': '',
                        'entrez_id': gene_id,
                        'ensembl_id': '',
                        'species': '',
                        'aliases': [],
                        'description': '',
                        'source': 'ncbi'
                    }
                    
                    # Extract information from XML
                    for item in root.findall('.//Item'):
                        name = item.get('Name', '')
                        text = item.text or ''
                        
                        if name == 'Name':
                            gene_info['hugo_symbol'] = text
                        elif name == 'Description':
                            gene_info['description'] = text
                        elif name == 'OtherAliases':
                            if text:
                                gene_info['aliases'] = [alias.strip() for alias in text.split(',')]
                        elif name == 'Organism':
                            gene_info['species'] = text
                    
                    return gene_info if gene_info['hugo_symbol'] else None
                
                return None
                
        except Exception as e:
            print(f"Error getting NCBI gene details: {str(e)}")
            return None
    
    async def batch_convert_genes(self, gene_list: list, species: str) -> Dict[str, Dict[str, Any]]:
        """
        Convert multiple genes at once.
        
        Args:
            gene_list: List of gene identifiers
            species: Species name
            
        Returns:
            Dictionary mapping input genes to their canonical information
        """
        results = {}
        
        # Process genes in batches to avoid overwhelming APIs
        batch_size = 5
        for i in range(0, len(gene_list), batch_size):
            batch = gene_list[i:i + batch_size]
            
            # Process batch concurrently
            tasks = [self.get_canonical_gene_info(gene, species) for gene in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Store results
            for gene, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    results[gene] = {
                        'hugo_symbol': gene.upper(),
                        'entrez_id': None,
                        'ensembl_id': None,
                        'species': species,
                        'aliases': [],
                        'description': f'Error: {str(result)}',
                        'source': 'error'
                    }
                else:
                    results[gene] = result
            
            # Small delay between batches
            if i + batch_size < len(gene_list):
                await asyncio.sleep(1)
        
        return results


# Convenience function for single gene conversion
async def get_canonical_gene_info(gene_identifier: str, species: str) -> Dict[str, Any]:
    """
    Convenience function to get canonical gene information.
    
    Args:
        gene_identifier: Gene symbol, alias, or ID to normalize
        species: Species name
        
    Returns:
        Dictionary containing standardized gene identifiers
    """
    converter = GeneIDConverter()
    return await converter.get_canonical_gene_info(gene_identifier, species)


# Example usage and testing
async def test_gene_converter():
    """Test function for the gene ID converter."""
    converter = GeneIDConverter()
    
    test_genes = [
        ('TP53', 'Homo sapiens'),
        ('BRCA1', 'Homo sapiens'),
        ('p53', 'Mus musculus'),  # Test alias
        ('GAPDH', 'Homo sapiens'),
        ('InvalidGene123', 'Homo sapiens')  # Test invalid gene
    ]
    
    print("Testing Gene ID Converter:")
    print("=" * 50)
    
    for gene, species in test_genes:
        print(f"\nTesting: {gene} in {species}")
        result = await converter.get_canonical_gene_info(gene, species)
        
        print(f"  HUGO Symbol: {result.get('hugo_symbol', 'N/A')}")
        print(f"  Entrez ID: {result.get('entrez_id', 'N/A')}")
        print(f"  Ensembl ID: {result.get('ensembl_id', 'N/A')}")
        print(f"  Aliases: {result.get('aliases', [])}")
        print(f"  Source: {result.get('source', 'N/A')}")


if __name__ == "__main__":
    asyncio.run(test_gene_converter())
