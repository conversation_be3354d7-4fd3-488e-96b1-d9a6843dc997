from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from typing import Optional, List, Dict, Any
import uuid
import asyncio
from datetime import datetime

from scrapers import PrimerScraper
from validator import <PERSON>lastValida<PERSON>
from context_extractor import extract_context, validate_extracted_context, enhance_context_with_defaults, BiologicalContext
from annotator import ClinicalAnnotator, LiteratureAnalyzer
from report_generator import ReportGenerator
from utils.id_converter import get_canonical_gene_info

app = FastAPI(title="Primer AI Platform API", version="1.0.0")

# Task tracking dictionary for asynchronous analysis
analysis_tasks: Dict[str, Dict[str, Any]] = {}

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request models
class AnalyzeRequest(BaseModel):
    gene: Optional[str] = ""
    species: Optional[str] = ""
    context: str

# Response models
class PrimerInfo(BaseModel):
    forward_primer: str
    reverse_primer: str
    tm_fwd: float
    tm_rev: float
    product_size: int
    source_db: str

class AnalyzeResponse(BaseModel):
    gene: str
    species: str
    context: str
    extracted_context: Optional[BiologicalContext] = None
    primers: List[PrimerInfo]
    validation_results: Optional[Dict[str, Any]] = None
    status: str
    message: str

class FullAnalysisResponse(BaseModel):
    gene: str
    species: str
    context: str
    extracted_context: Optional[BiologicalContext] = None
    canonical_gene_info: Optional[Dict[str, Any]] = None
    primers: List[PrimerInfo]
    validation_results: Optional[Dict[str, Any]] = None
    clinvar_data: List[Dict[str, Any]] = []
    kegg_pathways: List[Dict[str, Any]] = []
    literature: List[Dict[str, Any]] = []
    status: str
    message: str
    analysis_timestamp: str

class StartAnalysisResponse(BaseModel):
    task_id: str
    status: str
    message: str

class TaskStatusResponse(BaseModel):
    task_id: str
    status: str
    progress_message: str
    result: Optional[FullAnalysisResponse] = None
    error: Optional[str] = None

@app.get("/")
async def root():
    return {"message": "Welcome to Primer AI Platform API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

async def run_full_analysis_background(task_id: str, request: AnalyzeRequest):
    """
    Background function to run the full analysis workflow.
    Updates the task status as it progresses.
    """
    try:
        # Update status to running
        analysis_tasks[task_id]["status"] = "running"
        analysis_tasks[task_id]["progress_message"] = "Starting analysis..."

        # Step 1: Extract structured context
        analysis_tasks[task_id]["progress_message"] = "Extracting context from research description..."
        extracted_context = extract_context(request.context)
        validated_context = validate_extracted_context(extracted_context)
        final_context = enhance_context_with_defaults(
            validated_context,
            request.gene or "",
            request.species or ""
        )

        # Determine target gene and species
        target_gene = final_context.genes[0] if final_context.genes else (request.gene or "")
        target_species = final_context.species or request.species or "Homo sapiens"

        if not target_gene:
            analysis_tasks[task_id]["status"] = "failed"
            analysis_tasks[task_id]["error"] = "No gene specified in input or context"
            return

        # Step 1.5: Normalize gene identifiers
        analysis_tasks[task_id]["progress_message"] = "Normalizing gene identifiers..."
        canonical_gene_info = await get_canonical_gene_info(target_gene, target_species)
        canonical_gene = canonical_gene_info.get('hugo_symbol', target_gene)

        # Initialize analyzers
        primer_scraper = PrimerScraper()
        blast_validator = BlastValidator()
        clinical_annotator = ClinicalAnnotator()
        literature_analyzer = LiteratureAnalyzer()

        # Step 2: Scrape primers from PrimerBank
        analysis_tasks[task_id]["progress_message"] = "Searching for primer pairs in PrimerBank..."
        primers = await primer_scraper.search_primerbank(canonical_gene, target_species)
        primer_models = [PrimerInfo(**primer) for primer in primers] if primers else []

        # Step 3: Validate top primer pair with BLAST
        validation_results = None
        if primers:
            analysis_tasks[task_id]["progress_message"] = "Validating primer specificity with BLAST..."
            top_primer = primers[0]
            validation_results = await blast_validator.validate_primer_pair(
                top_primer['forward_primer'],
                top_primer['reverse_primer'],
                'nr'
            )

        # Step 4: Query ClinVar for clinical variants
        analysis_tasks[task_id]["progress_message"] = "Querying ClinVar for clinical variants..."
        clinvar_data = await clinical_annotator.query_clinvar(canonical_gene)

        # Step 5: Query KEGG for pathway information
        analysis_tasks[task_id]["progress_message"] = "Querying KEGG for pathway information..."
        kegg_pathways = await clinical_annotator.query_kegg(canonical_gene)

        # Step 6: Search literature
        analysis_tasks[task_id]["progress_message"] = "Searching relevant literature..."
        search_query = literature_analyzer.construct_search_query(final_context)
        literature = await literature_analyzer.search_literature(search_query, limit=5)

        # Create final result
        result = FullAnalysisResponse(
            gene=canonical_gene,
            species=target_species,
            context=request.context,
            extracted_context=final_context,
            canonical_gene_info=canonical_gene_info,
            primers=primer_models,
            validation_results=validation_results,
            clinvar_data=clinvar_data,
            kegg_pathways=kegg_pathways,
            literature=literature,
            status="success",
            message=f"Comprehensive analysis completed for {canonical_gene}. Found {len(primers)} primers, {len(clinvar_data)} ClinVar variants, {len(kegg_pathways)} KEGG pathways, and {len(literature)} relevant papers.",
            analysis_timestamp=datetime.now().isoformat()
        )

        # Update task with final result
        analysis_tasks[task_id]["status"] = "complete"
        analysis_tasks[task_id]["progress_message"] = "Analysis completed successfully"
        analysis_tasks[task_id]["result"] = result

    except Exception as e:
        # Handle any errors
        analysis_tasks[task_id]["status"] = "failed"
        analysis_tasks[task_id]["error"] = str(e)
        analysis_tasks[task_id]["progress_message"] = f"Analysis failed: {str(e)}"

@app.post("/api/start-analysis", response_model=StartAnalysisResponse)
async def start_analysis(request: AnalyzeRequest, background_tasks: BackgroundTasks):
    """
    Start an asynchronous analysis task.
    Returns a task ID that can be used to poll for status and results.
    """
    # Generate unique task ID
    task_id = str(uuid.uuid4())

    # Initialize task in tracking dictionary
    analysis_tasks[task_id] = {
        "status": "pending",
        "progress_message": "Analysis queued",
        "result": None,
        "error": None,
        "created_at": datetime.now().isoformat()
    }

    # Start background analysis
    background_tasks.add_task(run_full_analysis_background, task_id, request)

    return StartAnalysisResponse(
        task_id=task_id,
        status="pending",
        message="Analysis started. Use the task ID to check progress."
    )

@app.get("/api/status/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """
    Get the status of an analysis task.
    Returns current status and results if completed.
    """
    if task_id not in analysis_tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    task = analysis_tasks[task_id]

    return TaskStatusResponse(
        task_id=task_id,
        status=task["status"],
        progress_message=task["progress_message"],
        result=task.get("result"),
        error=task.get("error")
    )

@app.post("/api/analyze", response_model=AnalyzeResponse)
async def analyze_primers(request: AnalyzeRequest):
    """
    Analyze primers for a given gene and species.

    This endpoint:
    1. Extracts structured context from user's research description
    2. Uses extracted or provided gene/species information
    3. Scrapes primer data from PrimerBank
    4. Validates the top primer pair using BLAST
    5. Returns comprehensive analysis results
    """
    try:
        # Step 1: Extract structured context from user's description
        print(f"Extracting context from: {request.context[:100]}...")
        extracted_context = extract_context(request.context)
        validated_context = validate_extracted_context(extracted_context)

        # Step 2: Enhance context with user inputs if needed
        final_context = enhance_context_with_defaults(
            validated_context,
            request.gene or "",
            request.species or ""
        )

        # Determine which gene and species to use for analysis
        target_gene = final_context.genes[0] if final_context.genes else (request.gene or "")
        target_species = final_context.species or request.species or "Homo sapiens"

        if not target_gene:
            return AnalyzeResponse(
                gene=request.gene or "",
                species=request.species or "",
                context=request.context,
                extracted_context=final_context,
                primers=[],
                validation_results=None,
                status="insufficient_input",
                message="No gene specified in input or context. Please provide a gene symbol."
            )

        # Initialize scrapers and validators
        primer_scraper = PrimerScraper()
        blast_validator = BlastValidator()

        # Step 3: Scrape primers from PrimerBank
        print(f"Searching for primers: {target_gene} in {target_species}")
        primers = await primer_scraper.search_primerbank(target_gene, target_species)

        if not primers:
            return AnalyzeResponse(
                gene=target_gene,
                species=target_species,
                context=request.context,
                extracted_context=final_context,
                primers=[],
                validation_results=None,
                status="no_primers_found",
                message=f"No primers found for {target_gene} in {target_species}"
            )

        # Convert to Pydantic models
        primer_models = [PrimerInfo(**primer) for primer in primers]

        # Step 4: Validate the top primer pair with BLAST
        validation_results = None
        if primers:
            top_primer = primers[0]  # Use the first (presumably best) primer
            print(f"Validating top primer pair with BLAST...")

            validation_results = await blast_validator.validate_primer_pair(
                top_primer['forward_primer'],
                top_primer['reverse_primer'],
                'nr'  # Use NCBI nr database
            )

        return AnalyzeResponse(
            gene=target_gene,
            species=target_species,
            context=request.context,
            extracted_context=final_context,
            primers=primer_models,
            validation_results=validation_results,
            status="success",
            message=f"Found {len(primers)} primer pairs for {target_gene} and validated the top candidate"
        )

    except Exception as e:
        print(f"Error in analyze_primers: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Analysis failed: {str(e)}"
        )

@app.post("/api/full_analysis", response_model=FullAnalysisResponse)
async def full_analysis(request: AnalyzeRequest):
    """
    Comprehensive primer analysis workflow.

    This endpoint orchestrates the complete analysis pipeline:
    1. Extract structured context from user's research description
    2. Scrape primer data from PrimerBank
    3. Validate primer specificity using BLAST
    4. Query ClinVar for clinical variants
    5. Query KEGG for pathway information
    6. Search literature for relevant papers
    7. Return comprehensive analysis results
    """
    from datetime import datetime

    try:
        # Step 1: Extract structured context
        print(f"Starting full analysis for context: {request.context[:100]}...")
        extracted_context = extract_context(request.context)
        validated_context = validate_extracted_context(extracted_context)
        final_context = enhance_context_with_defaults(
            validated_context,
            request.gene or "",
            request.species or ""
        )

        # Determine target gene and species
        target_gene = final_context.genes[0] if final_context.genes else (request.gene or "")
        target_species = final_context.species or request.species or "Homo sapiens"

        if not target_gene:
            return FullAnalysisResponse(
                gene=request.gene or "",
                species=request.species or "",
                context=request.context,
                extracted_context=final_context,
                primers=[],
                validation_results=None,
                clinvar_data=[],
                kegg_pathways=[],
                literature=[],
                status="insufficient_input",
                message="No gene specified in input or context. Please provide a gene symbol.",
                analysis_timestamp=datetime.now().isoformat()
            )

        # Step 1.5: Normalize gene identifiers
        print(f"Normalizing gene identifier: {target_gene}")
        canonical_gene_info = await get_canonical_gene_info(target_gene, target_species)

        # Use the canonical gene symbol for all downstream analyses
        canonical_gene = canonical_gene_info.get('hugo_symbol', target_gene)
        entrez_id = canonical_gene_info.get('entrez_id')

        print(f"Canonical gene info: {canonical_gene} (Entrez: {entrez_id})")

        # Initialize all analyzers
        primer_scraper = PrimerScraper()
        blast_validator = BlastValidator()
        clinical_annotator = ClinicalAnnotator()
        literature_analyzer = LiteratureAnalyzer()

        # Step 2: Scrape primers from PrimerBank
        print(f"Searching for primers: {canonical_gene} in {target_species}")
        primers = await primer_scraper.search_primerbank(canonical_gene, target_species)
        primer_models = [PrimerInfo(**primer) for primer in primers] if primers else []

        # Step 3: Validate top primer pair with BLAST
        validation_results = None
        if primers:
            print("Validating top primer pair with BLAST...")
            top_primer = primers[0]
            validation_results = await blast_validator.validate_primer_pair(
                top_primer['forward_primer'],
                top_primer['reverse_primer'],
                'nr'
            )

        # Step 4: Query ClinVar for clinical variants (use canonical gene symbol)
        print(f"Querying ClinVar for {canonical_gene}...")
        clinvar_data = await clinical_annotator.query_clinvar(canonical_gene)

        # Step 5: Query KEGG for pathway information (use canonical gene symbol)
        print(f"Querying KEGG pathways for {canonical_gene}...")
        kegg_pathways = await clinical_annotator.query_kegg(canonical_gene)

        # Step 6: Search literature
        print("Searching relevant literature...")
        search_query = literature_analyzer.construct_search_query(final_context)
        literature = await literature_analyzer.search_literature(search_query, limit=5)

        return FullAnalysisResponse(
            gene=canonical_gene,
            species=target_species,
            context=request.context,
            extracted_context=final_context,
            canonical_gene_info=canonical_gene_info,
            primers=primer_models,
            validation_results=validation_results,
            clinvar_data=clinvar_data,
            kegg_pathways=kegg_pathways,
            literature=literature,
            status="success",
            message=f"Comprehensive analysis completed for {canonical_gene} (normalized from '{target_gene}'). Found {len(primers)} primers, {len(clinvar_data)} ClinVar variants, {len(kegg_pathways)} KEGG pathways, and {len(literature)} relevant papers.",
            analysis_timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        print(f"Error in full_analysis: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Full analysis failed: {str(e)}"
        )

@app.post("/api/generate_report")
async def generate_report(analysis_data: Dict[str, Any]):
    """
    Generate a formatted Markdown report from analysis data.

    This endpoint takes the comprehensive analysis results and generates
    a professional Markdown report with narrative summaries.
    """
    try:
        report_generator = ReportGenerator()
        markdown_report = report_generator.create_markdown_report(analysis_data)

        return {
            "report": markdown_report,
            "status": "success",
            "message": "Report generated successfully"
        }

    except Exception as e:
        print(f"Error generating report: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Report generation failed: {str(e)}"
        )

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
