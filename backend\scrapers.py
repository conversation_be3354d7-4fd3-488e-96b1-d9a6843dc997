import asyncio
from typing import List, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>
from bs4 import BeautifulSoup
import re


class PrimerScraper:
    """
    Async scraper for PrimerBank database to extract primer pairs.
    """
    
    def __init__(self):
        self.primerbank_url = "https://pga.mgh.harvard.edu/primerbank/"
    
    async def search_primerbank(self, gene_symbol: str, species: str) -> List[Dict[str, Any]]:
        """
        Search PrimerBank for primer pairs for a given gene and species.
        
        Args:
            gene_symbol: Gene symbol to search for (e.g., "TP53")
            species: Species name (e.g., "Homo sapiens")
            
        Returns:
            List of dictionaries containing primer information
        """
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                # Navigate to PrimerBank
                await page.goto(self.primerbank_url)
                await page.wait_for_load_state('networkidle')
                
                # Fill in the search form
                await page.fill('input[name="gene"]', gene_symbol)
                
                # Select species from dropdown if available
                species_selector = 'select[name="species"]'
                if await page.locator(species_selector).count() > 0:
                    await page.select_option(species_selector, label=species)
                
                # Submit the search
                await page.click('input[type="submit"]')
                await page.wait_for_load_state('networkidle')
                
                # Get the page content
                content = await page.content()
                
                # Parse the results
                primers = self._parse_primerbank_results(content)
                
                return primers
                
            except Exception as e:
                print(f"Error scraping PrimerBank: {str(e)}")
                return []
            finally:
                await browser.close()
    
    def _parse_primerbank_results(self, html_content: str) -> List[Dict[str, Any]]:
        """
        Parse PrimerBank HTML results to extract primer information.
        
        Args:
            html_content: HTML content from PrimerBank results page
            
        Returns:
            List of primer dictionaries
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        primers = []
        
        # Look for primer tables or result sections
        # PrimerBank typically displays results in tables
        tables = soup.find_all('table')
        
        for table in tables:
            rows = table.find_all('tr')
            
            for row in rows[1:]:  # Skip header row
                cells = row.find_all(['td', 'th'])
                
                if len(cells) >= 5:  # Ensure we have enough columns
                    try:
                        # Extract primer information
                        # This is a generic parser - actual structure may vary
                        primer_data = {
                            'forward_primer': self._extract_sequence(cells[0].get_text(strip=True)),
                            'reverse_primer': self._extract_sequence(cells[1].get_text(strip=True)),
                            'tm_fwd': self._extract_tm(cells[2].get_text(strip=True)),
                            'tm_rev': self._extract_tm(cells[3].get_text(strip=True)),
                            'product_size': self._extract_product_size(cells[4].get_text(strip=True)),
                            'source_db': 'PrimerBank'
                        }
                        
                        # Only add if we have valid primer sequences
                        if primer_data['forward_primer'] and primer_data['reverse_primer']:
                            primers.append(primer_data)
                            
                    except (IndexError, ValueError) as e:
                        continue  # Skip malformed rows
        
        # If no primers found in tables, try alternative parsing
        if not primers:
            primers = self._parse_alternative_format(soup)
        
        return primers
    
    def _extract_sequence(self, text: str) -> str:
        """Extract DNA sequence from text."""
        # Look for DNA sequences (A, T, G, C characters)
        sequence_match = re.search(r'[ATGC]{15,}', text.upper())
        return sequence_match.group(0) if sequence_match else ""
    
    def _extract_tm(self, text: str) -> float:
        """Extract melting temperature from text."""
        # Look for temperature values
        tm_match = re.search(r'(\d+\.?\d*)\s*°?C?', text)
        try:
            return float(tm_match.group(1)) if tm_match else 0.0
        except ValueError:
            return 0.0
    
    def _extract_product_size(self, text: str) -> int:
        """Extract product size from text."""
        # Look for product size in base pairs
        size_match = re.search(r'(\d+)\s*bp', text)
        try:
            return int(size_match.group(1)) if size_match else 0
        except ValueError:
            return 0
    
    def _parse_alternative_format(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        Alternative parsing method for different PrimerBank formats.
        """
        primers = []
        
        # Look for primer sequences in different HTML structures
        primer_sections = soup.find_all(['div', 'p'], class_=re.compile(r'primer|result'))
        
        for section in primer_sections:
            text = section.get_text()
            
            # Try to extract primer pairs from text
            forward_match = re.search(r'Forward[:\s]*([ATGC]{15,})', text.upper())
            reverse_match = re.search(r'Reverse[:\s]*([ATGC]{15,})', text.upper())
            
            if forward_match and reverse_match:
                primer_data = {
                    'forward_primer': forward_match.group(1),
                    'reverse_primer': reverse_match.group(1),
                    'tm_fwd': self._extract_tm(text),
                    'tm_rev': self._extract_tm(text),
                    'product_size': self._extract_product_size(text),
                    'source_db': 'PrimerBank'
                }
                primers.append(primer_data)
        
        return primers


# Example usage and testing
async def test_primer_scraper():
    """Test function for the PrimerScraper class."""
    scraper = PrimerScraper()
    results = await scraper.search_primerbank("TP53", "Homo sapiens")
    
    print(f"Found {len(results)} primer pairs:")
    for i, primer in enumerate(results[:3]):  # Show first 3 results
        print(f"\nPrimer {i+1}:")
        print(f"  Forward: {primer['forward_primer']}")
        print(f"  Reverse: {primer['reverse_primer']}")
        print(f"  Tm Forward: {primer['tm_fwd']}°C")
        print(f"  Tm Reverse: {primer['tm_rev']}°C")
        print(f"  Product Size: {primer['product_size']} bp")


if __name__ == "__main__":
    asyncio.run(test_primer_scraper())
