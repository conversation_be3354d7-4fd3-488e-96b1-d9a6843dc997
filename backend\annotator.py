import asyncio
import aiohttp
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional
import json
import time
from semanticscholar import SemanticScholar


class ClinicalAnnotator:
    """
    Class for querying clinical databases like ClinVar and KEGG for gene annotation.
    """
    
    def __init__(self):
        self.ncbi_base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils"
        self.kegg_base_url = "https://rest.kegg.jp"
        self.request_delay = 0.5  # Delay between requests to be respectful to APIs
    
    async def query_clinvar(self, gene_symbol: str) -> List[Dict[str, Any]]:
        """
        Query ClinVar database for variants associated with a gene.
        
        Args:
            gene_symbol: Gene symbol to search for
            
        Returns:
            List of dictionaries containing variant information
        """
        try:
            async with aiohttp.ClientSession() as session:
                # Step 1: Search for variants in ClinVar
                search_ids = await self._search_clinvar_variants(session, gene_symbol)
                
                if not search_ids:
                    return []
                
                # Step 2: Get detailed information for the variants
                variants = await self._fetch_clinvar_details(session, search_ids[:20])  # Limit to top 20
                
                return variants
                
        except Exception as e:
            print(f"Error querying ClinVar: {str(e)}")
            return []
    
    async def _search_clinvar_variants(self, session: aiohttp.ClientSession, gene_symbol: str) -> List[str]:
        """
        Search ClinVar for variant IDs associated with a gene.
        """
        search_url = f"{self.ncbi_base_url}/esearch.fcgi"
        params = {
            'db': 'clinvar',
            'term': f'{gene_symbol}[gene]',
            'retmax': '50',
            'retmode': 'xml'
        }
        
        try:
            async with session.get(search_url, params=params) as response:
                if response.status == 200:
                    content = await response.text()
                    root = ET.fromstring(content)
                    
                    # Extract variant IDs
                    id_list = root.find('.//IdList')
                    if id_list is not None:
                        return [id_elem.text for id_elem in id_list.findall('Id')]
                
                return []
                
        except Exception as e:
            print(f"Error searching ClinVar: {str(e)}")
            return []
    
    async def _fetch_clinvar_details(self, session: aiohttp.ClientSession, variant_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Fetch detailed information for ClinVar variants.
        """
        if not variant_ids:
            return []
        
        summary_url = f"{self.ncbi_base_url}/esummary.fcgi"
        params = {
            'db': 'clinvar',
            'id': ','.join(variant_ids),
            'retmode': 'xml'
        }
        
        try:
            await asyncio.sleep(self.request_delay)  # Be respectful to the API
            
            async with session.get(summary_url, params=params) as response:
                if response.status == 200:
                    content = await response.text()
                    return self._parse_clinvar_xml(content)
                
                return []
                
        except Exception as e:
            print(f"Error fetching ClinVar details: {str(e)}")
            return []
    
    def _parse_clinvar_xml(self, xml_content: str) -> List[Dict[str, Any]]:
        """
        Parse ClinVar XML response to extract variant information.
        """
        variants = []
        
        try:
            root = ET.fromstring(xml_content)
            
            for doc_sum in root.findall('.//DocSum'):
                variant_data = {
                    'id': '',
                    'title': '',
                    'clinical_significance': '',
                    'variant_type': '',
                    'molecular_consequence': '',
                    'condition': '',
                    'review_status': ''
                }
                
                # Extract ID
                id_elem = doc_sum.find('Id')
                if id_elem is not None:
                    variant_data['id'] = id_elem.text
                
                # Extract other fields from Items
                for item in doc_sum.findall('.//Item'):
                    name = item.get('Name', '')
                    text = item.text or ''
                    
                    if name == 'title':
                        variant_data['title'] = text
                    elif name == 'clinical_significance':
                        variant_data['clinical_significance'] = text
                    elif name == 'variation_type':
                        variant_data['variant_type'] = text
                    elif name == 'molecular_consequence':
                        variant_data['molecular_consequence'] = text
                    elif name == 'condition_list':
                        variant_data['condition'] = text
                    elif name == 'review_status':
                        variant_data['review_status'] = text
                
                if variant_data['id']:
                    variants.append(variant_data)
        
        except ET.ParseError as e:
            print(f"Error parsing ClinVar XML: {str(e)}")
        
        return variants
    
    async def query_kegg(self, gene_symbol: str) -> List[Dict[str, Any]]:
        """
        Query KEGG database for pathways associated with a gene.
        
        Args:
            gene_symbol: Gene symbol to search for
            
        Returns:
            List of dictionaries containing pathway information
        """
        try:
            async with aiohttp.ClientSession() as session:
                # Step 1: Find KEGG gene ID
                kegg_gene_id = await self._find_kegg_gene_id(session, gene_symbol)
                
                if not kegg_gene_id:
                    return []
                
                # Step 2: Get pathways for the gene
                pathways = await self._fetch_kegg_pathways(session, kegg_gene_id)
                
                return pathways
                
        except Exception as e:
            print(f"Error querying KEGG: {str(e)}")
            return []
    
    async def _find_kegg_gene_id(self, session: aiohttp.ClientSession, gene_symbol: str) -> Optional[str]:
        """
        Find KEGG gene ID for a given gene symbol.
        """
        # Search in human genes first
        search_url = f"{self.kegg_base_url}/find/hsa/{gene_symbol}"
        
        try:
            await asyncio.sleep(self.request_delay)  # Be respectful to the API
            
            async with session.get(search_url) as response:
                if response.status == 200:
                    content = await response.text()
                    lines = content.strip().split('\n')
                    
                    for line in lines:
                        if line and '\t' in line:
                            gene_id = line.split('\t')[0]
                            if gene_id.startswith('hsa:'):
                                return gene_id
                
                return None
                
        except Exception as e:
            print(f"Error finding KEGG gene ID: {str(e)}")
            return None
    
    async def _fetch_kegg_pathways(self, session: aiohttp.ClientSession, kegg_gene_id: str) -> List[Dict[str, Any]]:
        """
        Fetch KEGG pathways for a gene ID.
        """
        pathways_url = f"{self.kegg_base_url}/link/pathway/{kegg_gene_id}"
        
        try:
            await asyncio.sleep(self.request_delay)  # Be respectful to the API
            
            async with session.get(pathways_url) as response:
                if response.status == 200:
                    content = await response.text()
                    pathway_ids = []
                    
                    lines = content.strip().split('\n')
                    for line in lines:
                        if line and '\t' in line:
                            parts = line.split('\t')
                            if len(parts) >= 2:
                                pathway_id = parts[1]
                                pathway_ids.append(pathway_id)
                    
                    # Get pathway details
                    pathways = []
                    for pathway_id in pathway_ids[:10]:  # Limit to top 10 pathways
                        pathway_info = await self._get_kegg_pathway_info(session, pathway_id)
                        if pathway_info:
                            pathways.append(pathway_info)
                    
                    return pathways
                
                return []
                
        except Exception as e:
            print(f"Error fetching KEGG pathways: {str(e)}")
            return []
    
    async def _get_kegg_pathway_info(self, session: aiohttp.ClientSession, pathway_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information for a KEGG pathway.
        """
        info_url = f"{self.kegg_base_url}/get/{pathway_id}"
        
        try:
            await asyncio.sleep(self.request_delay)  # Be respectful to the API
            
            async with session.get(info_url) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # Parse pathway information
                    pathway_info = {
                        'id': pathway_id,
                        'name': '',
                        'description': '',
                        'class': '',
                        'organism': ''
                    }
                    
                    lines = content.split('\n')
                    current_section = ''
                    
                    for line in lines:
                        if line.startswith('NAME'):
                            pathway_info['name'] = line.replace('NAME', '').strip()
                        elif line.startswith('DESCRIPTION'):
                            pathway_info['description'] = line.replace('DESCRIPTION', '').strip()
                        elif line.startswith('CLASS'):
                            pathway_info['class'] = line.replace('CLASS', '').strip()
                        elif line.startswith('ORGANISM'):
                            pathway_info['organism'] = line.replace('ORGANISM', '').strip()
                    
                    return pathway_info
                
                return None
                
        except Exception as e:
            print(f"Error getting KEGG pathway info: {str(e)}")
            return None


class LiteratureAnalyzer:
    """
    Class for searching and analyzing scientific literature using Semantic Scholar.
    """

    def __init__(self):
        self.sch = SemanticScholar()

    async def search_literature(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search for relevant scientific papers using Semantic Scholar.

        Args:
            query: Search query constructed from biological context
            limit: Maximum number of papers to return

        Returns:
            List of dictionaries containing paper information
        """
        try:
            # Run the synchronous Semantic Scholar search in a thread pool
            loop = asyncio.get_event_loop()
            papers = await loop.run_in_executor(
                None,
                self._search_papers_sync,
                query,
                limit
            )

            return papers

        except Exception as e:
            print(f"Error searching literature: {str(e)}")
            return []

    def _search_papers_sync(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """
        Synchronous method to search papers (to be run in thread pool).
        """
        try:
            # Search for papers
            results = self.sch.search_paper(
                query,
                limit=limit,
                fields=['title', 'abstract', 'authors', 'year', 'citationCount', 'url', 'tldr']
            )

            papers = []
            for paper in results:
                paper_data = {
                    'title': paper.title or 'No title available',
                    'abstract': paper.abstract or 'No abstract available',
                    'authors': [author.name for author in (paper.authors or [])],
                    'year': paper.year,
                    'citation_count': paper.citationCount or 0,
                    'url': paper.url,
                    'tldr': paper.tldr.get('text') if paper.tldr else 'No TLDR available',
                    'paper_id': paper.paperId
                }
                papers.append(paper_data)

            return papers

        except Exception as e:
            print(f"Error in synchronous paper search: {str(e)}")
            return []

    def construct_search_query(self, biological_context) -> str:
        """
        Construct a search query from biological context.

        Args:
            biological_context: BiologicalContext object

        Returns:
            Formatted search query string
        """
        query_parts = []

        # Add genes
        if biological_context.genes:
            gene_query = " OR ".join(biological_context.genes)
            query_parts.append(f"({gene_query})")

        # Add disease context if available
        if biological_context.disease_context:
            query_parts.append(biological_context.disease_context)

        # Add species if not human (human is default in most searches)
        if biological_context.species and biological_context.species.lower() != "homo sapiens":
            species_name = biological_context.species.split()[-1]  # Get species name
            query_parts.append(species_name)

        # Add research focus keywords
        if biological_context.research_focus:
            # Extract key terms from research focus
            focus_keywords = self._extract_keywords(biological_context.research_focus)
            if focus_keywords:
                query_parts.extend(focus_keywords)

        return " ".join(query_parts)

    def _extract_keywords(self, text: str) -> List[str]:
        """
        Extract relevant keywords from research focus text.
        """
        # Simple keyword extraction - could be enhanced with NLP
        keywords = []

        # Common research terms to look for
        research_terms = [
            'expression', 'regulation', 'function', 'pathway', 'interaction',
            'mutation', 'variant', 'polymorphism', 'cancer', 'disease',
            'therapy', 'treatment', 'drug', 'resistance', 'biomarker',
            'diagnosis', 'prognosis', 'mechanism', 'signaling'
        ]

        text_lower = text.lower()
        for term in research_terms:
            if term in text_lower:
                keywords.append(term)

        return keywords[:3]  # Limit to top 3 keywords


# Example usage and testing
async def test_clinical_annotator():
    """Test function for the ClinicalAnnotator class."""
    annotator = ClinicalAnnotator()

    # Test ClinVar query
    print("Testing ClinVar query for TP53...")
    clinvar_results = await annotator.query_clinvar("TP53")
    print(f"Found {len(clinvar_results)} ClinVar variants")

    if clinvar_results:
        print("Sample variant:")
        print(f"  ID: {clinvar_results[0].get('id', 'N/A')}")
        print(f"  Title: {clinvar_results[0].get('title', 'N/A')[:100]}...")
        print(f"  Clinical Significance: {clinvar_results[0].get('clinical_significance', 'N/A')}")

    # Test KEGG query
    print("\nTesting KEGG query for TP53...")
    kegg_results = await annotator.query_kegg("TP53")
    print(f"Found {len(kegg_results)} KEGG pathways")

    if kegg_results:
        print("Sample pathway:")
        print(f"  ID: {kegg_results[0].get('id', 'N/A')}")
        print(f"  Name: {kegg_results[0].get('name', 'N/A')}")
        print(f"  Class: {kegg_results[0].get('class', 'N/A')}")


async def test_literature_analyzer():
    """Test function for the LiteratureAnalyzer class."""
    analyzer = LiteratureAnalyzer()

    # Test literature search
    print("Testing literature search for TP53 cancer...")
    papers = await analyzer.search_literature("TP53 cancer mutation", limit=3)
    print(f"Found {len(papers)} papers")

    for i, paper in enumerate(papers, 1):
        print(f"\nPaper {i}:")
        print(f"  Title: {paper['title'][:100]}...")
        print(f"  Authors: <AUTHORS>
        print(f"  Year: {paper['year']}")
        print(f"  Citations: {paper['citation_count']}")
        print(f"  TLDR: {paper['tldr'][:100]}...")


if __name__ == "__main__":
    asyncio.run(test_clinical_annotator())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_literature_analyzer())
