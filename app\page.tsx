"use client";

import { useState } from "react";

export default function Home() {
  const [geneSymbol, setGeneSymbol] = useState("");
  const [species, setSpecies] = useState("");
  const [researchContext, setResearchContext] = useState("");

  const handleStartAnalysis = () => {
    // TODO: Implement analysis logic
    console.log("Starting analysis with:", {
      geneSymbol,
      species,
      researchContext,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Main Heading */}
          <h1 className="text-4xl md:text-5xl font-bold text-center text-gray-800 dark:text-white mb-8">
            Advanced AI Primer Analysis System
          </h1>

          {/* Main Form Card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 mb-8">
            <div className="space-y-6">
              {/* Gene Symbol Input */}
              <div>
                <label htmlFor="geneSymbol" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Gene Symbol
                </label>
                <input
                  type="text"
                  id="geneSymbol"
                  value={geneSymbol}
                  onChange={(e) => setGeneSymbol(e.target.value)}
                  placeholder="e.g., TP53"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                />
              </div>

              {/* Species Input */}
              <div>
                <label htmlFor="species" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Species
                </label>
                <input
                  type="text"
                  id="species"
                  value={species}
                  onChange={(e) => setSpecies(e.target.value)}
                  placeholder="e.g., Homo sapiens"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                />
              </div>

              {/* Research Context Textarea */}
              <div>
                <label htmlFor="researchContext" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Research Context
                </label>
                <textarea
                  id="researchContext"
                  value={researchContext}
                  onChange={(e) => setResearchContext(e.target.value)}
                  placeholder="e.g., Investigating the role of TP53 in lung cancer drug resistance"
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors resize-vertical"
                />
              </div>

              {/* Start Analysis Button */}
              <div className="flex justify-center pt-4">
                <button
                  onClick={handleStartAnalysis}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  Start Analysis
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
