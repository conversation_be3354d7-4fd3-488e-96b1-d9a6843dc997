"use client";

import { useState, useEffect } from "react";
import ReactMarkdown from "react-markdown";

export default function Home() {
  const [geneSymbol, setGeneSymbol] = useState("");
  const [species, setSpecies] = useState("");
  const [researchContext, setResearchContext] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [progressMessage, setProgressMessage] = useState("");
  const [taskId, setTaskId] = useState("");
  const [analysisResults, setAnalysisResults] = useState(null);
  const [report, setReport] = useState("");
  const [error, setError] = useState("");

  // Polling mechanism for task status
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (taskId && isLoading) {
      intervalId = setInterval(async () => {
        try {
          const response = await fetch(`http://localhost:8000/api/status/${taskId}`);

          if (!response.ok) {
            throw new Error(`Status check failed: ${response.statusText}`);
          }

          const statusData = await response.json();

          // Update progress message
          setProgressMessage(statusData.progress_message || "Processing...");

          if (statusData.status === "complete") {
            // Analysis completed successfully
            clearInterval(intervalId);
            setIsLoading(false);
            setAnalysisResults(statusData.result);

            // Generate report
            if (statusData.result) {
              await generateReport(statusData.result);
            }

          } else if (statusData.status === "failed") {
            // Analysis failed
            clearInterval(intervalId);
            setIsLoading(false);
            setError(statusData.error || "Analysis failed");
            setTaskId("");
          }

        } catch (err) {
          console.error("Error checking task status:", err);
          setError(err.message || "Failed to check analysis status");
          clearInterval(intervalId);
          setIsLoading(false);
          setTaskId("");
        }
      }, 3000); // Poll every 3 seconds
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [taskId, isLoading]);

  const generateReport = async (analysisData: any) => {
    try {
      const reportResponse = await fetch("http://localhost:8000/api/generate_report", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(analysisData),
      });

      if (!reportResponse.ok) {
        throw new Error(`Report generation failed: ${reportResponse.statusText}`);
      }

      const reportData = await reportResponse.json();
      setReport(reportData.report);
    } catch (err) {
      console.error("Error generating report:", err);
      setError(err.message || "Failed to generate report");
    }
  };

  const handleStartAnalysis = async () => {
    if (!researchContext.trim() && !geneSymbol.trim()) {
      setError("Please provide either a gene symbol or research context.");
      return;
    }

    setIsLoading(true);
    setError("");
    setAnalysisResults(null);
    setReport("");
    setProgressMessage("Starting analysis...");

    try {
      // Start asynchronous analysis
      const response = await fetch("http://localhost:8000/api/start-analysis", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          gene: geneSymbol,
          species: species,
          context: researchContext,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to start analysis: ${response.statusText}`);
      }

      const data = await response.json();
      setTaskId(data.task_id);
      setProgressMessage("Analysis queued. Please wait...");

    } catch (err) {
      setError(err.message || "Failed to start analysis");
      setIsLoading(false);
      console.error("Analysis start error:", err);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Main Heading */}
          <h1 className="text-4xl md:text-5xl font-bold text-center text-gray-800 dark:text-white mb-8">
            Advanced AI Primer Analysis System
          </h1>

          {/* Main Form Card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 mb-8">
            <div className="space-y-6">
              {/* Gene Symbol Input */}
              <div>
                <label htmlFor="geneSymbol" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Gene Symbol
                </label>
                <input
                  type="text"
                  id="geneSymbol"
                  value={geneSymbol}
                  onChange={(e) => setGeneSymbol(e.target.value)}
                  placeholder="e.g., TP53"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                />
              </div>

              {/* Species Input */}
              <div>
                <label htmlFor="species" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Species
                </label>
                <input
                  type="text"
                  id="species"
                  value={species}
                  onChange={(e) => setSpecies(e.target.value)}
                  placeholder="e.g., Homo sapiens"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                />
              </div>

              {/* Research Context Textarea */}
              <div>
                <label htmlFor="researchContext" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Research Context
                </label>
                <textarea
                  id="researchContext"
                  value={researchContext}
                  onChange={(e) => setResearchContext(e.target.value)}
                  placeholder="e.g., Investigating the role of TP53 in lung cancer drug resistance"
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors resize-vertical"
                />
              </div>

              {/* Start Analysis Button */}
              <div className="flex justify-center pt-4">
                <button
                  onClick={handleStartAnalysis}
                  disabled={isLoading}
                  className={`font-semibold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg ${
                    isLoading
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-blue-600 hover:bg-blue-700 hover:shadow-xl transform hover:scale-105"
                  } text-white`}
                >
                  {isLoading ? "Analyzing..." : "Start Analysis"}
                </button>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="max-w-4xl mx-auto mt-6">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Analysis Error</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{error}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Loading Indicator with Progress */}
          {isLoading && (
            <div className="max-w-4xl mx-auto mt-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-lg font-medium text-blue-800">Running Analysis</h3>
                    <p className="text-blue-600">{progressMessage || "This may take a few minutes. Please wait..."}</p>
                    {taskId && (
                      <p className="text-sm text-blue-500 mt-1">Task ID: {taskId}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Analysis Results Summary */}
          {analysisResults && !isLoading && (
            <div className="max-w-4xl mx-auto mt-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-green-800 mb-2">Analysis Complete</h3>
                <div className="text-sm text-green-700 space-y-1">
                  <p><strong>Gene:</strong> {analysisResults.gene}</p>
                  <p><strong>Species:</strong> {analysisResults.species}</p>
                  <p><strong>Primers Found:</strong> {analysisResults.primers?.length || 0}</p>
                  <p><strong>ClinVar Variants:</strong> {analysisResults.clinvar_data?.length || 0}</p>
                  <p><strong>KEGG Pathways:</strong> {analysisResults.kegg_pathways?.length || 0}</p>
                  <p><strong>Literature Papers:</strong> {analysisResults.literature?.length || 0}</p>
                </div>
              </div>
            </div>
          )}

          {/* Generated Report */}
          {report && (
            <div className="max-w-4xl mx-auto mt-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8">
                <div className="prose prose-lg max-w-none dark:prose-invert">
                  <ReactMarkdown>{report}</ReactMarkdown>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
