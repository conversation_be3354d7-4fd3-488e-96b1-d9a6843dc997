from typing import Dict, Any, List
from datetime import datetime
import json


class ReportGenerator:
    """
    Class for generating structured Markdown reports from analysis data.
    """
    
    def __init__(self):
        pass
    
    def create_markdown_report(self, analysis_data: Dict[str, Any]) -> str:
        """
        Create a comprehensive Markdown report from analysis data.
        
        Args:
            analysis_data: Dictionary containing all analysis results
            
        Returns:
            Formatted Markdown report as string
        """
        report_sections = []
        
        # Header
        report_sections.append(self._create_header(analysis_data))
        
        # Introduction
        report_sections.append(self._create_introduction(analysis_data))
        
        # Primer Information
        report_sections.append(self._create_primer_section(analysis_data))
        
        # BLAST Specificity Analysis
        report_sections.append(self._create_blast_section(analysis_data))
        
        # Clinical Significance
        report_sections.append(self._create_clinical_section(analysis_data))
        
        # Literature Review
        report_sections.append(self._create_literature_section(analysis_data))
        
        # Footer
        report_sections.append(self._create_footer(analysis_data))
        
        return "\n\n".join(report_sections)
    
    def _create_header(self, data: Dict[str, Any]) -> str:
        """Create report header."""
        gene = data.get('gene', 'Unknown')
        species = data.get('species', 'Unknown')
        timestamp = data.get('analysis_timestamp', datetime.now().isoformat())
        
        return f"""# Primer Analysis Report: {gene}

**Species:** {species}  
**Analysis Date:** {timestamp}  
**Status:** {data.get('status', 'Unknown')}

---"""
    
    def _create_introduction(self, data: Dict[str, Any]) -> str:
        """Create introduction section."""
        gene = data.get('gene', 'Unknown')
        context = data.get('context', '')
        extracted_context = data.get('extracted_context', {})
        
        intro = f"""## Introduction

This report presents a comprehensive primer analysis for the gene **{gene}**."""
        
        if context:
            intro += f"""

**Research Context:** {context}"""
        
        if extracted_context:
            if extracted_context.get('disease_context'):
                intro += f"""

**Disease Context:** {extracted_context['disease_context']}"""
            
            if extracted_context.get('research_focus'):
                intro += f"""

**Research Focus:** {extracted_context['research_focus']}"""
        
        return intro
    
    def _create_primer_section(self, data: Dict[str, Any]) -> str:
        """Create primer information section."""
        primers = data.get('primers', [])
        
        section = """## Primer Information"""
        
        if not primers:
            section += "\n\nNo primers were found for the specified gene and species."
            return section
        
        section += f"\n\nA total of **{len(primers)}** primer pairs were identified from PrimerBank.\n"
        
        # Create primer table
        section += """
| # | Forward Primer (5' → 3') | Reverse Primer (5' → 3') | Tm Fwd (°C) | Tm Rev (°C) | Product Size (bp) |
|---|--------------------------|--------------------------|-------------|-------------|-------------------|"""
        
        for i, primer in enumerate(primers[:10], 1):  # Show top 10 primers
            fwd = primer.get('forward_primer', 'N/A')
            rev = primer.get('reverse_primer', 'N/A')
            tm_fwd = primer.get('tm_fwd', 0)
            tm_rev = primer.get('tm_rev', 0)
            size = primer.get('product_size', 0)
            
            section += f"\n| {i} | `{fwd}` | `{rev}` | {tm_fwd} | {tm_rev} | {size} |"
        
        return section
    
    def _create_blast_section(self, data: Dict[str, Any]) -> str:
        """Create BLAST specificity analysis section."""
        validation = data.get('validation_results', {})
        
        section = """## BLAST Specificity Analysis"""
        
        if not validation or 'error' in validation:
            error_msg = validation.get('error', 'BLAST analysis could not be completed')
            section += f"\n\n⚠️ **Analysis Status:** {error_msg}"
            return section
        
        specificity_score = validation.get('specificity_score', 0)
        target_match = validation.get('target_match', {})
        off_targets = validation.get('off_target_hits', [])
        
        section += f"\n\n**Specificity Score:** {specificity_score}/1.0"
        
        if specificity_score >= 0.8:
            section += " ✅ (Excellent)"
        elif specificity_score >= 0.6:
            section += " ⚠️ (Good)"
        else:
            section += " ❌ (Poor)"
        
        if target_match:
            section += f"""

### Target Match
- **Accession:** {target_match.get('accession', 'N/A')}
- **Title:** {target_match.get('title', 'N/A')}
- **Forward Primer Score:** {target_match.get('forward_score', 'N/A')}
- **E-value:** {target_match.get('forward_evalue', 'N/A')}"""
        
        if off_targets:
            section += f"""

### Potential Off-Target Hits ({len(off_targets)} found)"""
            
            for i, hit in enumerate(off_targets[:5], 1):  # Show top 5 off-targets
                section += f"""
{i}. **{hit.get('title', 'Unknown')}**
   - Accession: {hit.get('accession', 'N/A')}
   - Score: {hit.get('score', 'N/A')}
   - E-value: {hit.get('evalue', 'N/A')}"""
        else:
            section += "\n\n✅ **No significant off-target hits detected.**"
        
        return section
    
    def _create_clinical_section(self, data: Dict[str, Any]) -> str:
        """Create clinical significance section."""
        clinvar_data = data.get('clinvar_data', [])
        kegg_pathways = data.get('kegg_pathways', [])
        gene = data.get('gene', 'Unknown')
        
        section = f"""## Clinical Significance

This section provides clinical and functional annotation for {gene}."""
        
        # ClinVar variants
        section += "\n\n### ClinVar Variants"
        
        if not clinvar_data:
            section += f"\n\nNo ClinVar variants were found for {gene}."
        else:
            section += f"\n\nA total of **{len(clinvar_data)}** variants were found in ClinVar.\n"
            
            # Group variants by clinical significance
            significance_groups = {}
            for variant in clinvar_data:
                sig = variant.get('clinical_significance', 'Unknown')
                if sig not in significance_groups:
                    significance_groups[sig] = []
                significance_groups[sig].append(variant)
            
            for significance, variants in significance_groups.items():
                section += f"\n**{significance}:** {len(variants)} variants"
        
        # KEGG pathways
        section += "\n\n### KEGG Pathways"
        
        if not kegg_pathways:
            section += f"\n\nNo KEGG pathway information was found for {gene}."
        else:
            section += f"\n\n{gene} is associated with **{len(kegg_pathways)}** KEGG pathways:\n"
            
            for i, pathway in enumerate(kegg_pathways[:10], 1):  # Show top 10 pathways
                name = pathway.get('name', 'Unknown pathway')
                pathway_id = pathway.get('id', 'N/A')
                section += f"\n{i}. **{name}** ({pathway_id})"
                
                if pathway.get('class'):
                    section += f"\n   - Class: {pathway['class']}"
        
        return section
    
    def _create_literature_section(self, data: Dict[str, Any]) -> str:
        """Create literature review section."""
        literature = data.get('literature', [])
        gene = data.get('gene', 'Unknown')
        
        section = f"""## Relevant Literature

Recent publications related to {gene} research:"""
        
        if not literature:
            section += f"\n\nNo recent literature was found for {gene}."
            return section
        
        section += f"\n\nFound **{len(literature)}** relevant publications:\n"
        
        for i, paper in enumerate(literature, 1):
            title = paper.get('title', 'Unknown title')
            authors = paper.get('authors', [])
            year = paper.get('year', 'Unknown')
            citations = paper.get('citation_count', 0)
            tldr = paper.get('tldr', 'No summary available')
            url = paper.get('url', '')
            
            section += f"""
### {i}. {title}

**Authors: <AUTHORS>
**Year:** {year} | **Citations:** {citations}"""
            
            if url:
                section += f" | [Link]({url})"
            
            section += f"""

**Summary:** {tldr}"""
        
        return section
    
    def _create_footer(self, data: Dict[str, Any]) -> str:
        """Create report footer."""
        return """---

## Report Information

This report was generated by the Advanced AI Primer Analysis System. The analysis includes:

- **Primer Design:** PrimerBank database search
- **Specificity Validation:** NCBI BLAST analysis
- **Clinical Annotation:** ClinVar variant database
- **Pathway Analysis:** KEGG pathway database
- **Literature Review:** Semantic Scholar search

For questions or technical support, please refer to the system documentation.

*Report generated automatically - please verify all results independently.*"""


# Example usage and testing
def test_report_generator():
    """Test function for the ReportGenerator class."""
    # Sample analysis data
    sample_data = {
        'gene': 'TP53',
        'species': 'Homo sapiens',
        'context': 'Investigating TP53 mutations in lung cancer',
        'extracted_context': {
            'genes': ['TP53'],
            'species': 'Homo sapiens',
            'disease_context': 'lung cancer',
            'research_focus': 'investigating mutations'
        },
        'primers': [
            {
                'forward_primer': 'ATGGAGGAGCCGCAGTCAGAT',
                'reverse_primer': 'TCAGTCTGAGTCAGGCCCTTC',
                'tm_fwd': 60.5,
                'tm_rev': 59.8,
                'product_size': 234,
                'source_db': 'PrimerBank'
            }
        ],
        'validation_results': {
            'specificity_score': 0.85,
            'target_match': {
                'accession': 'NM_000546',
                'title': 'Homo sapiens tumor protein p53',
                'forward_score': 42,
                'forward_evalue': 1e-10
            },
            'off_target_hits': []
        },
        'clinvar_data': [
            {
                'id': '12345',
                'clinical_significance': 'Pathogenic',
                'title': 'TP53 variant'
            }
        ],
        'kegg_pathways': [
            {
                'id': 'hsa04115',
                'name': 'p53 signaling pathway',
                'class': 'Cellular Processes'
            }
        ],
        'literature': [
            {
                'title': 'TP53 mutations in cancer',
                'authors': ['Smith J', 'Doe A'],
                'year': 2023,
                'citation_count': 45,
                'tldr': 'This study examines TP53 mutations in various cancer types.',
                'url': 'https://example.com/paper1'
            }
        ],
        'status': 'success',
        'analysis_timestamp': '2024-01-15T10:30:00'
    }
    
    generator = ReportGenerator()
    report = generator.create_markdown_report(sample_data)
    
    print("Generated Report:")
    print("=" * 50)
    print(report)


if __name__ == "__main__":
    test_report_generator()
