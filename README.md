# Advanced AI Primer Analysis System

A comprehensive bioinformatics platform that combines primer design, validation, and analysis with AI-powered context extraction and report generation.

## 🧬 Features

- **Intelligent Context Extraction**: Uses OpenAI GPT to extract structured biological context from research descriptions
- **Primer Database Search**: Automated scraping of PrimerBank for primer pairs
- **BLAST Validation**: Specificity analysis using NCBI BLAST API
- **Clinical Annotation**: Integration with ClinVar and KEGG databases
- **Literature Analysis**: Semantic Scholar integration for relevant research papers
- **AI-Generated Reports**: Professional Markdown reports with narrative summaries
- **Modern Web Interface**: React/Next.js frontend with real-time analysis

## 🏗️ Architecture

### Frontend (Next.js)
- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS
- **Components**: React with hooks for state management
- **Markdown Rendering**: react-markdown for report display

### Backend (FastAPI)
- **Framework**: FastAPI with async support
- **Web Scraping**: Playwright for PrimerBank
- **AI Integration**: LangChain + OpenAI for context extraction
- **External APIs**: NCBI BLAST, ClinVar, KEGG, Semantic Scholar
- **Report Generation**: Custom Markdown generator with AI narratives

## 📋 Prerequisites

- **Node.js** 18+ and npm
- **Python** 3.8+
- **OpenAI API Key** (required for context extraction and report generation)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd primer-ai-platform
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install

# Set up environment variables
cp .env.example .env
# Edit .env and add your OpenAI API key
```

### 3. Frontend Setup

```bash
# Navigate to project root
cd ..

# Install Node.js dependencies
npm install
```

### 4. Running the Application

#### Start the Backend (Terminal 1)
```bash
cd backend
python main.py
```
The API will be available at `http://localhost:8000`

#### Start the Frontend (Terminal 2)
```bash
npm run dev
```
The web interface will be available at `http://localhost:3000`

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the `backend/` directory:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
SEMANTIC_SCHOLAR_API_KEY=your_semantic_scholar_api_key_here
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
FRONTEND_URL=http://localhost:3000
```

## 📚 API Documentation

The FastAPI backend automatically generates interactive API documentation:

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### Main Endpoints

#### `POST /api/analyze`
Basic primer analysis with context extraction.

**Request Body:**
```json
{
  "gene": "TP53",
  "species": "Homo sapiens",
  "context": "Investigating TP53 role in lung cancer drug resistance"
}
```

#### `POST /api/full_analysis`
Comprehensive analysis including all databases and literature search.

#### `POST /api/generate_report`
Generate formatted Markdown report from analysis data.

## 🧪 Usage Examples

### Basic Analysis
1. Open the web interface at `http://localhost:3000`
2. Enter a gene symbol (e.g., "TP53")
3. Specify species (e.g., "Homo sapiens")
4. Provide research context (e.g., "Studying p53 mutations in cancer")
5. Click "Start Analysis"

### Research Context Examples
- "Investigating BRCA1 mutations in breast cancer patients"
- "Analyzing GAPDH expression as housekeeping gene in mouse liver"
- "Studying COVID-19 spike protein interactions in human cells"

## 🏭 Production Deployment

### Backend (Docker)

Create a `Dockerfile` in the project root:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# Copy backend files
COPY backend/ ./backend/
COPY backend/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright
RUN playwright install --with-deps chromium

# Expose port
EXPOSE 8000

# Run the application
CMD ["python", "backend/main.py"]
```

Build and run:
```bash
docker build -t primer-ai-backend .
docker run -p 8000:8000 --env-file backend/.env primer-ai-backend
```

### Frontend (Vercel)

```bash
# Build for production
npm run build

# Test production build locally
npm start
```

For Vercel deployment:
1. Connect your GitHub repository to Vercel
2. Set environment variable: `NEXT_PUBLIC_API_BASE_URL=https://your-backend-url`
3. Deploy automatically on push to main branch

## 🧪 Testing

### Backend Tests
```bash
cd backend
pip install pytest pytest-asyncio
pytest tests/
```

### Frontend Tests
```bash
npm test
```

## 📁 Project Structure

```
primer-ai-platform/
├── app/                    # Next.js app directory
│   └── page.tsx           # Main application page
├── backend/               # FastAPI backend
│   ├── main.py           # FastAPI application
│   ├── scrapers.py       # PrimerBank scraper
│   ├── validator.py      # BLAST validation
│   ├── annotator.py      # Clinical annotation
│   ├── context_extractor.py # AI context extraction
│   ├── report_generator.py  # Report generation
│   ├── requirements.txt  # Python dependencies
│   ├── .env.example     # Environment template
│   └── tests/           # Backend tests
├── public/              # Static assets
├── package.json         # Node.js dependencies
├── tailwind.config.js   # Tailwind configuration
├── next.config.js       # Next.js configuration
└── README.md           # This file
```

## 🔬 Analysis Workflow

1. **Context Extraction**: AI analyzes research description to extract genes, species, disease context
2. **Primer Search**: Automated scraping of PrimerBank database
3. **Specificity Validation**: BLAST analysis for off-target detection
4. **Clinical Annotation**: ClinVar variants and KEGG pathway analysis
5. **Literature Review**: Semantic Scholar search for relevant papers
6. **Report Generation**: AI-enhanced Markdown report with narrative summaries

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions or issues:
1. Check the API documentation at `http://localhost:8000/docs`
2. Review the console logs for error messages
3. Ensure all environment variables are properly set
4. Verify API keys are valid and have sufficient quota

## 🚧 Future Roadmap

- **CI/CD Pipeline**: GitHub Actions for automated testing and deployment
- **User Authentication**: User accounts and analysis history
- **Interactive Visualizations**: Cytoscape.js for network visualization
- **Additional Databases**: Integration with more primer and annotation databases
- **Batch Processing**: Support for multiple gene analysis
- **Export Options**: PDF and Excel report formats
