import os
from typing import List, Optional
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import PromptTemplate
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class BiologicalContext(BaseModel):
    """
    Pydantic model for structured biological context extraction.
    """
    genes: List[str] = Field(description="List of gene symbols mentioned in the context")
    species: str = Field(description="Species name (e.g., 'Homo sapiens', 'Mus musculus')")
    disease_context: Optional[str] = Field(description="Disease or condition being studied, if any")
    research_focus: str = Field(description="Main research focus or objective")


def extract_context(text: str) -> BiologicalContext:
    """
    Extract structured biological context from user's research text using LangChain and OpenAI.
    
    Args:
        text: User's research context description
        
    Returns:
        BiologicalContext: Structured context information
    """
    # Initialize the OpenAI model
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OPENAI_API_KEY environment variable is required")
    
    llm = ChatOpenAI(
        model="gpt-3.5-turbo",
        temperature=0,
        openai_api_key=api_key
    )
    
    # Set up the output parser
    parser = PydanticOutputParser(pydantic_object=BiologicalContext)
    
    # Create the prompt template with few-shot examples
    prompt_template = PromptTemplate(
        template="""You are an expert bioinformatician. Your task is to extract structured information from research context descriptions.

Given a research context, extract the following information:
- genes: List of gene symbols mentioned (use standard HUGO nomenclature)
- species: The species being studied (use scientific names like 'Homo sapiens')
- disease_context: Any disease or medical condition mentioned (null if none)
- research_focus: The main research objective or focus

Here are some examples:

Example 1:
Input: "Investigating the role of TP53 in lung cancer drug resistance mechanisms in human cell lines"
Output: {{
    "genes": ["TP53"],
    "species": "Homo sapiens",
    "disease_context": "lung cancer",
    "research_focus": "investigating TP53 role in drug resistance mechanisms"
}}

Example 2:
Input: "Studying BRCA1 and BRCA2 expression patterns in mouse breast tissue development"
Output: {{
    "genes": ["BRCA1", "BRCA2"],
    "species": "Mus musculus",
    "disease_context": null,
    "research_focus": "studying expression patterns in breast tissue development"
}}

Example 3:
Input: "Analyzing GAPDH as a housekeeping gene for qPCR normalization in rat liver samples"
Output: {{
    "genes": ["GAPDH"],
    "species": "Rattus norvegicus",
    "disease_context": null,
    "research_focus": "analyzing housekeeping gene for qPCR normalization"
}}

Now extract information from this research context:

Research Context: {text}

{format_instructions}

Extracted Information:""",
        input_variables=["text"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # Create the chain
    chain = prompt_template | llm | parser
    
    try:
        # Extract the context
        result = chain.invoke({"text": text})
        return result
    
    except Exception as e:
        # Fallback: create a basic context if extraction fails
        print(f"Context extraction failed: {str(e)}")
        return BiologicalContext(
            genes=[],
            species="Homo sapiens",  # Default to human
            disease_context=None,
            research_focus=text[:100] + "..." if len(text) > 100 else text
        )


def validate_extracted_context(context: BiologicalContext) -> BiologicalContext:
    """
    Validate and clean up extracted context.
    
    Args:
        context: Extracted biological context
        
    Returns:
        BiologicalContext: Validated and cleaned context
    """
    # Clean up gene symbols (remove duplicates, standardize format)
    cleaned_genes = []
    for gene in context.genes:
        gene_clean = gene.strip().upper()
        if gene_clean and gene_clean not in cleaned_genes:
            cleaned_genes.append(gene_clean)
    
    # Standardize species names
    species_mapping = {
        "human": "Homo sapiens",
        "humans": "Homo sapiens",
        "mouse": "Mus musculus",
        "mice": "Mus musculus",
        "rat": "Rattus norvegicus",
        "rats": "Rattus norvegicus",
        "zebrafish": "Danio rerio",
        "fruit fly": "Drosophila melanogaster",
        "drosophila": "Drosophila melanogaster",
        "yeast": "Saccharomyces cerevisiae",
        "e. coli": "Escherichia coli",
        "escherichia coli": "Escherichia coli"
    }
    
    species_clean = context.species.lower().strip()
    standardized_species = species_mapping.get(species_clean, context.species)
    
    # Clean up disease context
    disease_clean = None
    if context.disease_context:
        disease_clean = context.disease_context.strip().lower()
        if disease_clean in ["none", "n/a", "na", ""]:
            disease_clean = None
        else:
            disease_clean = context.disease_context.strip()
    
    # Clean up research focus
    focus_clean = context.research_focus.strip()
    if not focus_clean:
        focus_clean = "General research study"
    
    return BiologicalContext(
        genes=cleaned_genes,
        species=standardized_species,
        disease_context=disease_clean,
        research_focus=focus_clean
    )


def enhance_context_with_defaults(context: BiologicalContext, gene_input: str, species_input: str) -> BiologicalContext:
    """
    Enhance extracted context with user inputs if context extraction was incomplete.
    
    Args:
        context: Extracted biological context
        gene_input: User's gene input
        species_input: User's species input
        
    Returns:
        BiologicalContext: Enhanced context
    """
    # Use user inputs as fallback if extraction didn't find genes/species
    enhanced_genes = context.genes if context.genes else [gene_input.strip().upper()] if gene_input.strip() else []
    enhanced_species = context.species if context.species != "Homo sapiens" or not species_input.strip() else species_input.strip()
    
    return BiologicalContext(
        genes=enhanced_genes,
        species=enhanced_species,
        disease_context=context.disease_context,
        research_focus=context.research_focus
    )


# Example usage and testing
def test_context_extractor():
    """Test function for the context extractor."""
    test_contexts = [
        "Investigating the role of TP53 in lung cancer drug resistance",
        "Studying BRCA1 and BRCA2 mutations in breast cancer patients",
        "Analyzing GAPDH expression as a control gene in mouse liver samples",
        "Research on COVID-19 spike protein interactions in human cells"
    ]
    
    for i, test_text in enumerate(test_contexts, 1):
        print(f"\nTest {i}: {test_text}")
        try:
            result = extract_context(test_text)
            validated = validate_extracted_context(result)
            
            print(f"  Genes: {validated.genes}")
            print(f"  Species: {validated.species}")
            print(f"  Disease: {validated.disease_context}")
            print(f"  Focus: {validated.research_focus}")
        except Exception as e:
            print(f"  Error: {str(e)}")


if __name__ == "__main__":
    test_context_extractor()
