{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/primer-ai-platform/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nexport default function Home() {\n  const [geneSymbol, setGeneSymbol] = useState(\"\");\n  const [species, setSpecies] = useState(\"\");\n  const [researchContext, setResearchContext] = useState(\"\");\n\n  const handleStartAnalysis = () => {\n    // TODO: Implement analysis logic\n    console.log(\"Starting analysis with:\", {\n      geneSymbol,\n      species,\n      researchContext,\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Main Heading */}\n          <h1 className=\"text-4xl md:text-5xl font-bold text-center text-gray-800 dark:text-white mb-8\">\n            Advanced AI Primer Analysis System\n          </h1>\n\n          {/* Main Form Card */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 mb-8\">\n            <div className=\"space-y-6\">\n              {/* Gene Symbol Input */}\n              <div>\n                <label htmlFor=\"geneSymbol\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Gene Symbol\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"geneSymbol\"\n                  value={geneSymbol}\n                  onChange={(e) => setGeneSymbol(e.target.value)}\n                  placeholder=\"e.g., TP53\"\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors\"\n                />\n              </div>\n\n              {/* Species Input */}\n              <div>\n                <label htmlFor=\"species\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Species\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"species\"\n                  value={species}\n                  onChange={(e) => setSpecies(e.target.value)}\n                  placeholder=\"e.g., Homo sapiens\"\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors\"\n                />\n              </div>\n\n              {/* Research Context Textarea */}\n              <div>\n                <label htmlFor=\"researchContext\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Research Context\n                </label>\n                <textarea\n                  id=\"researchContext\"\n                  value={researchContext}\n                  onChange={(e) => setResearchContext(e.target.value)}\n                  placeholder=\"e.g., Investigating the role of TP53 in lung cancer drug resistance\"\n                  rows={6}\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors resize-vertical\"\n                />\n              </div>\n\n              {/* Start Analysis Button */}\n              <div className=\"flex justify-center pt-4\">\n                <button\n                  onClick={handleStartAnalysis}\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\"\n                >\n                  Start Analysis\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,sBAAsB;QAC1B,iCAAiC;QACjC,QAAQ,GAAG,CAAC,2BAA2B;YACrC;YACA;YACA;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAG,WAAU;kCAAgF;;;;;;kCAK9F,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAAkE;;;;;;sDAGxG,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAKd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAAkE;;;;;;sDAGrG,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAKd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAkE;;;;;;sDAG7G,8OAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,aAAY;4CACZ,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/primer-ai-platform/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/primer-ai-platform/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/primer-ai-platform/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}